<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WooCommerce Adres Kartları - Demo</title>
    <link rel="stylesheet" href="assets/css/address-cards.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            margin: 0;
            padding: 40px 20px;
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .demo-title {
            font-size: 36px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .demo-subtitle {
            color: #6c757d;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">WooCommerce Adres Kartları</h1>
            <p class="demo-subtitle">Modern ve kullanıcı dostu kart tasarımı</p>
        </div>

        <div class="woo-custom-addresses-header">
            <h2 class="addresses-title">Adreslerim</h2>
            <p class="addresses-description">
                Aşağıdaki adresler ödeme sayfasında varsayılan olarak kullanılacaktır.
            </p>
        </div>

        <div class="woo-custom-addresses-container">
            <!-- Fatura Adresi - Dolu -->
            <div class="woo-custom-address-card billing-address">
                <div class="address-card-header">
                    <div class="address-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="address-title-section">
                        <h3 class="address-title">Fatura adresi</h3>
                        <span class="address-type">Faturalama için</span>
                    </div>
                    <div class="address-actions">
                        <a href="#" class="edit-address-btn">
                            <i class="fas fa-edit"></i>
                            <span>Düzenle</span>
                        </a>
                    </div>
                </div>

                <div class="address-card-content">
                    <div class="address-details">
                        Ahmet Yılmaz<br>
                        Atatürk Mahallesi<br>
                        İstiklal Caddesi No: 123<br>
                        34000 Beşiktaş/İstanbul<br>
                        Türkiye<br>
                        Tel: +90 ************
                    </div>
                </div>

                <div class="address-card-footer">
                    <div class="address-status">
                        <i class="fas fa-check-circle"></i>
                        <span>Aktif adres</span>
                    </div>
                </div>
            </div>

            <!-- Gönderim Adresi - Boş -->
            <div class="woo-custom-address-card shipping-address">
                <div class="address-card-header">
                    <div class="address-icon">
                        <i class="fas fa-shipping-fast"></i>
                    </div>
                    <div class="address-title-section">
                        <h3 class="address-title">Gönderim adresi</h3>
                        <span class="address-type">Teslimat için</span>
                    </div>
                    <div class="address-actions">
                        <a href="#" class="edit-address-btn">
                            <i class="fas fa-edit"></i>
                            <span>Ekle</span>
                        </a>
                    </div>
                </div>

                <div class="address-card-content">
                    <div class="address-empty">
                        <div class="empty-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <p class="empty-text">Henüz bu tür bir adres eklenmemiş.</p>
                        <a href="#" class="add-address-btn">
                            <i class="fas fa-plus"></i>
                            Gönderim adresi Ekle
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Özellikler -->
        <div style="margin-top: 60px; padding: 30px; background: #f8f9fa; border-radius: 15px;">
            <h3 style="color: #2c3e50; margin-bottom: 20px; text-align: center;">✨ Özellikler</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div style="text-align: center;">
                    <i class="fas fa-mobile-alt" style="font-size: 24px; color: #667eea; margin-bottom: 10px;"></i>
                    <h4 style="margin: 10px 0 5px; color: #2c3e50;">Responsive Tasarım</h4>
                    <p style="color: #6c757d; margin: 0; font-size: 14px;">Tüm cihazlarda mükemmel görünüm</p>
                </div>
                <div style="text-align: center;">
                    <i class="fas fa-palette" style="font-size: 24px; color: #764ba2; margin-bottom: 10px;"></i>
                    <h4 style="margin: 10px 0 5px; color: #2c3e50;">Modern Tasarım</h4>
                    <p style="color: #6c757d; margin: 0; font-size: 14px;">Gradient renkler ve animasyonlar</p>
                </div>
                <div style="text-align: center;">
                    <i class="fas fa-rocket" style="font-size: 24px; color: #28a745; margin-bottom: 10px;"></i>
                    <h4 style="margin: 10px 0 5px; color: #2c3e50;">Hızlı Yükleme</h4>
                    <p style="color: #6c757d; margin: 0; font-size: 14px;">Optimize edilmiş CSS ve animasyonlar</p>
                </div>
                <div style="text-align: center;">
                    <i class="fas fa-universal-access" style="font-size: 24px; color: #dc3545; margin-bottom: 10px;"></i>
                    <h4 style="margin: 10px 0 5px; color: #2c3e50;">Erişilebilirlik</h4>
                    <p style="color: #6c757d; margin: 0; font-size: 14px;">WCAG standartlarına uygun</p>
                </div>
            </div>
        </div>

        <!-- Kurulum Bilgisi -->
        <div style="margin-top: 40px; padding: 20px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 15px; color: white; text-align: center;">
            <h3 style="margin: 0 0 10px; color: white;">🚀 Kurulum Tamamlandı!</h3>
            <p style="margin: 0; opacity: 0.9;">WooCommerce hesabım/adresler sayfanız artık modern kart tasarımı ile güncellendi.</p>
        </div>
    </div>

    <script>
        // Demo için hover efektleri
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.woo-custom-address-card');
            
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
            
            // Buton hover efektleri
            const buttons = document.querySelectorAll('.edit-address-btn, .add-address-btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    alert('Bu bir demo sayfasıdır. Gerçek WooCommerce sitesinde çalışacaktır.');
                });
            });
        });
    </script>
</body>
</html>
